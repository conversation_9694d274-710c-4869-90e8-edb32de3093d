/**
 * 团队列表内容组件
 */

import {
  CheckCircleOutlined,
  CrownOutlined,
  EyeOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import {
  Avatar,
  Button,
  Empty,
  Input,
  List,
  message,
  Space,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { AuthService, TeamService } from '@/services';
import type { TeamDetailResponse } from '@/types/api';
import { getTeamIdFromCurrentToken, hasTeamInCurrentToken, getUserIdFromCurrentToken } from '@/utils/tokenUtils';
import { recordTeamSelection } from '@/utils/teamSelectionUtils';

const { Title, Text } = Typography;
const { Search } = Input;



const TeamListContent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [currentTeamId, setCurrentTeamId] = useState<number | null>(null);

  useEffect(() => {
    fetchTeams();
    // 获取当前团队ID
    const teamId = getTeamIdFromCurrentToken();
    setCurrentTeamId(teamId);
  }, []);

  useEffect(() => {
    // 过滤团队列表
    const filtered = teams.filter(
      (team) =>
        team.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (team.description &&
          team.description.toLowerCase().includes(searchText.toLowerCase())),
    );
    setFilteredTeams(filtered);
  }, [teams, searchText]);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      setTeams(teamList);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = () => {
    history.push('/personal-center');
  };

  const handleViewTeam = async (team: TeamDetailResponse) => {
    try {
      // 切换到该团队
      const response = await AuthService.teamLogin({ teamId: team.id });

      // 检查后端返回的团队选择成功标识
      if (
        response.teamSelectionSuccess &&
        response.team &&
        response.team.id === team.id
      ) {
        message.success(`已切换到团队：${team.name}`);
        // 更新当前团队ID
        setCurrentTeamId(team.id);
        // 不需要刷新页面，让应用自然响应状态变化
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error: any) {
      console.error('切换团队失败:', error);

      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志
      // 如果是网络错误或其他非业务错误，才显示通用错误消息
      if (!error.message || error.message === 'Failed to fetch') {
        message.error('切换团队失败，请检查网络连接');
      }
    }
  };

  const handleSwitchTeam = async (team: TeamDetailResponse) => {
    try {
      const response = await AuthService.teamLogin({ teamId: team.id });

      // 检查后端返回的团队选择成功标识
      if (
        response.teamSelectionSuccess &&
        response.team &&
        response.team.id === team.id
      ) {
        message.success(`已切换到团队：${team.name}`);

        // 记录用户选择了这个团队
        const currentUserId = getUserIdFromCurrentToken();
        if (currentUserId) {
          recordTeamSelection(currentUserId, team.id);
        }

        // 更新当前团队ID
        setCurrentTeamId(team.id);

        // 等待一段时间确保 Token 更新完成后再跳转
        setTimeout(() => {
          history.push('/');
        }, 200);
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error: any) {
      console.error('切换团队失败:', error);
      // 错误已经在全局错误处理器中处理，这里只需要记录日志
    }
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索团队名称或描述"
          allowClear
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </div>

      {filteredTeams.length === 0 && !loading ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'
          }
        >
          {!searchText && (
            <Button type="primary" onClick={handleCreateTeam}>
              创建第一个团队
            </Button>
          )}
        </Empty>
      ) : (
        <List
          loading={loading}
          itemLayout="horizontal"
          dataSource={filteredTeams}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个团队`,
          }}
          renderItem={(team) => {
            const isCurrentTeam = currentTeamId === team.id;
            return (
              <List.Item
                style={{
                  backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,
                  border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,
                  borderRadius: isCurrentTeam ? '8px' : undefined,
                  padding: isCurrentTeam ? '16px' : undefined,
                }}
                actions={[
                  <Button
                    key="view"
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handleViewTeam(team)}
                  >
                    查看详情
                  </Button>,
                  isCurrentTeam ? (
                    <Tag
                      key="current"
                      color="green"
                      icon={<CheckCircleOutlined />}
                    >
                      当前团队
                    </Tag>
                  ) : (
                    <Button
                      key="switch"
                      type="primary"
                      size="small"
                      onClick={() => handleSwitchTeam(team)}
                    >
                      进入团队
                    </Button>
                  ),
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size={64}
                      icon={<TeamOutlined />}
                      style={{
                        backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',
                        border: isCurrentTeam ? '2px solid #389e0d' : undefined,
                      }}
                    />
                  }
                  title={
                    <Space>
                      <Title level={4} style={{ margin: 0 }}>
                        {team.name}
                      </Title>
                      {isCurrentTeam && (
                        <Tag color="green" icon={<CheckCircleOutlined />}>
                          当前团队
                        </Tag>
                      )}
                      {team.isCreator && (
                        <Tag color="gold" icon={<CrownOutlined />}>
                          创建者
                        </Tag>
                      )}
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size="small">
                      {team.description && (
                        <Text type="secondary">{team.description}</Text>
                      )}
                      <Space>
                        <Space size="small">
                          <UserOutlined />
                          <Text type="secondary">{team.memberCount} 名成员</Text>
                        </Space>
                        {team.assignedAt && (
                          <Text type="secondary">
                            加入于 {new Date(team.assignedAt).toLocaleDateString()}
                          </Text>
                        )}
                        <Text type="secondary">
                          创建于 {new Date(team.createdAt).toLocaleDateString()}
                        </Text>
                      </Space>
                      <Space>
                        <Text type="secondary">状态：</Text>
                        <Tag color={team.isActive ? 'green' : 'red'}>
                          {team.isActive ? '启用' : '停用'}
                        </Tag>
                        {team.lastAccessTime && (
                          <Text type="secondary">
                            最后访问：{new Date(team.lastAccessTime).toLocaleDateString()}
                          </Text>
                        )}
                      </Space>
                    </Space>
                  }
                />
              </List.Item>
            );
          }}
        />
      )}
    </div>
  );
};

export default TeamListContent;
