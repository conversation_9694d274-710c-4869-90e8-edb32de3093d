{"version": 3, "sources": ["umi.6481347425442892422.hot-update.js", "src/requestErrorConfig.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15490750833967241377';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import type { RequestOptions } from '@@/plugin-request/request';\nimport type { RequestConfig } from '@umijs/max';\nimport { history } from '@umijs/max';\nimport { message, notification } from 'antd';\nimport { AuthService } from '@/services';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError, ErrorDisplayType } from '@/utils/errorHandler';\n\n// 错误处理方案： 错误类型\nenum ErrorShowType {\n  SILENT = 0,\n  WARN_MESSAGE = 1,\n  ERROR_MESSAGE = 2,\n  NOTIFICATION = 3,\n  REDIRECT = 9,\n}\n\n// 与后端约定的响应数据格式\ninterface ApiResponseStructure {\n  code: number;\n  message: string;\n  data: any;\n  timestamp: string;\n}\n\n// 兼容原有格式\ninterface ResponseStructure {\n  success: boolean;\n  data: any;\n  errorCode?: number;\n  errorMessage?: string;\n  showType?: ErrorShowType;\n}\n\n/**\n * @name 错误处理\n * 适配团队管理系统的错误处理机制\n * @doc https://umijs.org/docs/max/request#配置\n */\nexport const errorConfig: RequestConfig = {\n  // 错误处理： umi@3 的错误处理方案。\n  errorConfig: {\n    // 错误抛出\n    errorThrower: (res) => {\n      // 检查是否是我们的API响应格式\n      const apiResponse = res as unknown as ApiResponseStructure;\n      if (apiResponse.code !== undefined) {\n        // 使用我们的API响应格式\n        if (apiResponse.code !== 200) {\n          const error: any = new Error(apiResponse.message);\n          error.name = 'ApiError';\n          error.info = {\n            errorCode: apiResponse.code,\n            errorMessage: apiResponse.message,\n            data: apiResponse.data,\n            showType:\n              apiResponse.code === 401\n                ? ErrorShowType.REDIRECT\n                : ErrorShowType.ERROR_MESSAGE,\n          };\n          throw error;\n        }\n      } else {\n        // 兼容原有格式\n        const { success, data, errorCode, errorMessage, showType } =\n          res as unknown as ResponseStructure;\n        if (!success) {\n          const error: any = new Error(errorMessage);\n          error.name = 'BizError';\n          error.info = { errorCode, errorMessage, showType, data };\n          throw error;\n        }\n      }\n    },\n    // 错误接收及处理\n    errorHandler: (error: any, opts: any) => {\n      if (opts?.skipErrorHandler) throw error;\n\n      // 我们的 API 错误\n      if (error.name === 'ApiError') {\n        const errorInfo = error.info;\n        if (errorInfo) {\n          const { errorMessage, errorCode } = errorInfo;\n\n          // 处理认证错误\n          if (errorCode === 401) {\n            // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题\n            const currentPath = window.location.pathname;\n            const isDashboardRelated =\n              currentPath.startsWith('/dashboard') ||\n              currentPath.startsWith('/team');\n\n            if (isDashboardRelated) {\n              console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n              // 不立即清除Token和跳转，让页面自己处理\n              return;\n            }\n\n            // 其他页面立即处理认证错误\n            AuthService.clearTokens();\n            message.error('登录已过期，请重新登录');\n            history.push('/user/login');\n            return;\n          }\n\n          // 处理权限错误\n          if (errorCode === 403) {\n            // 显示后端返回的具体错误消息\n            message.error(errorMessage || '没有权限访问该资源');\n            return;\n          }\n\n          // 处理其他业务错误\n          switch (errorInfo.showType) {\n            case ErrorShowType.SILENT:\n              // do nothing\n              break;\n            case ErrorShowType.WARN_MESSAGE:\n              message.warning(errorMessage);\n              break;\n            case ErrorShowType.ERROR_MESSAGE:\n              message.error(errorMessage);\n              break;\n            case ErrorShowType.NOTIFICATION:\n              notification.open({\n                description: errorMessage,\n                message: `错误代码: ${errorCode}`,\n              });\n              break;\n            case ErrorShowType.REDIRECT:\n              history.push('/user/login');\n              break;\n            default:\n              message.error(errorMessage);\n          }\n        }\n      }\n      // 兼容原有的 BizError\n      else if (error.name === 'BizError') {\n        const errorInfo: ResponseStructure | undefined = error.info;\n        if (errorInfo) {\n          const { errorMessage, errorCode } = errorInfo;\n          switch (errorInfo.showType) {\n            case ErrorShowType.SILENT:\n              break;\n            case ErrorShowType.WARN_MESSAGE:\n              message.warning(errorMessage);\n              break;\n            case ErrorShowType.ERROR_MESSAGE:\n              message.error(errorMessage);\n              break;\n            case ErrorShowType.NOTIFICATION:\n              notification.open({\n                description: errorMessage,\n                message: errorCode,\n              });\n              break;\n            case ErrorShowType.REDIRECT:\n              history.push('/user/login');\n              break;\n            default:\n              message.error(errorMessage);\n          }\n        }\n      }\n      // HTTP 错误\n      else if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          AuthService.clearTokens();\n          message.error('登录已过期，请重新登录');\n          history.push('/user/login');\n        } else if (status === 403) {\n          message.error('没有权限访问该资源');\n        } else if (status === 404) {\n          message.error('请求的资源不存在');\n        } else if (status >= 500) {\n          message.error('服务器错误，请稍后重试');\n        } else {\n          message.error(`请求失败: ${status}`);\n        }\n      }\n      // 网络错误\n      else if (error.request) {\n        message.error('网络错误，请检查网络连接');\n      }\n      // 其他错误\n      else {\n        message.error('请求失败，请重试');\n      }\n    },\n  },\n\n  // 请求拦截器\n  requestInterceptors: [\n    (config: RequestOptions) => {\n      // 添加认证头\n      const token = AuthService.getToken();\n      if (token) {\n        config.headers = {\n          ...config.headers,\n          Authorization: `Bearer ${token}`,\n        };\n      }\n\n      // 确保 Content-Type\n      if (!config.headers?.['Content-Type']) {\n        config.headers = {\n          ...config.headers,\n          'Content-Type': 'application/json',\n        };\n      }\n\n      return config;\n    },\n  ],\n\n  // 响应拦截器\n  responseInterceptors: [\n    (response) => {\n      // 拦截响应数据，进行个性化处理\n      const { data } = response;\n\n      // 检查是否是我们的API响应格式\n      if (data?.code !== undefined) {\n        // 如果是成功响应，直接返回\n        if (data.code === 200) {\n          return response;\n        }\n        // 错误响应会在 errorThrower 中处理\n      }\n\n      return response;\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCoCA;;;2BAAA;;;;;wCArCW;yCACc;6CACV;;;;;;;;;;YAI5B,eAAe;sBACV;;;;;;eAAA,kBAAA;YA8BE,MAAM,cAA6B;gBACxC,uBAAuB;gBACvB,aAAa;oBACX,OAAO;oBACP,cAAc,CAAC;wBACb,kBAAkB;wBAClB,MAAM,cAAc;wBACpB,IAAI,YAAY,IAAI,KAAK,WACvB,eAAe;wBACf;4BAAA,IAAI,YAAY,IAAI,KAAK,KAAK;gCAC5B,MAAM,QAAa,IAAI,MAAM,YAAY,OAAO;gCAChD,MAAM,IAAI,GAAG;gCACb,MAAM,IAAI,GAAG;oCACX,WAAW,YAAY,IAAI;oCAC3B,cAAc,YAAY,OAAO;oCACjC,MAAM,YAAY,IAAI;oCACtB,UACE,YAAY,IAAI,KAAK;gCAGzB;gCACA,MAAM;4BACR;wBAAA,OACK;4BACL,SAAS;4BACT,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,GACxD;4BACF,IAAI,CAAC,SAAS;gCACZ,MAAM,QAAa,IAAI,MAAM;gCAC7B,MAAM,IAAI,GAAG;gCACb,MAAM,IAAI,GAAG;oCAAE;oCAAW;oCAAc;oCAAU;gCAAK;gCACvD,MAAM;4BACR;wBACF;oBACF;oBACA,UAAU;oBACV,cAAc,CAAC,OAAY;wBACzB,IAAI,iBAAA,2BAAA,KAAM,gBAAgB,EAAE,MAAM;wBAElC,aAAa;wBACb,IAAI,MAAM,IAAI,KAAK,YAAY;4BAC7B,MAAM,YAAY,MAAM,IAAI;4BAC5B,IAAI,WAAW;gCACb,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;gCAEpC,SAAS;gCACT,IAAI,cAAc,KAAK;oCACrB,0CAA0C;oCAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oCAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oCAEzB,IAAI,oBAAoB;wCACtB,QAAQ,IAAI,CAAC;wCACb,wBAAwB;wCACxB;oCACF;oCAEA,eAAe;oCACf,qBAAW,CAAC,WAAW;oCACvB,aAAO,CAAC,KAAK,CAAC;oCACd,YAAO,CAAC,IAAI,CAAC;oCACb;gCACF;gCAEA,SAAS;gCACT,IAAI,cAAc,KAAK;oCACrB,gBAAgB;oCAChB,aAAO,CAAC,KAAK,CAAC,gBAAgB;oCAC9B;gCACF;gCAEA,WAAW;gCACX,OAAQ,UAAU,QAAQ;oCACxB;wCAEE;oCACF;wCACE,aAAO,CAAC,OAAO,CAAC;wCAChB;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;wCACd;oCACF;wCACE,kBAAY,CAAC,IAAI,CAAC;4CAChB,aAAa;4CACb,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;wCAC/B;wCACA;oCACF;wCACE,YAAO,CAAC,IAAI,CAAC;wCACb;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;gCAClB;4BACF;wBACF,OAEK,IAAI,MAAM,IAAI,KAAK,YAAY;4BAClC,MAAM,YAA2C,MAAM,IAAI;4BAC3D,IAAI,WAAW;gCACb,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;gCACpC,OAAQ,UAAU,QAAQ;oCACxB;wCACE;oCACF;wCACE,aAAO,CAAC,OAAO,CAAC;wCAChB;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;wCACd;oCACF;wCACE,kBAAY,CAAC,IAAI,CAAC;4CAChB,aAAa;4CACb,SAAS;wCACX;wCACA;oCACF;wCACE,YAAO,CAAC,IAAI,CAAC;wCACb;oCACF;wCACE,aAAO,CAAC,KAAK,CAAC;gCAClB;4BACF;wBACF,OAEK,IAAI,MAAM,QAAQ,EAAE;4BACvB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;4BACjC,IAAI,WAAW,KAAK;gCAClB,qBAAW,CAAC,WAAW;gCACvB,aAAO,CAAC,KAAK,CAAC;gCACd,YAAO,CAAC,IAAI,CAAC;4BACf,OAAO,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;iCACT,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;iCACT,IAAI,UAAU,KACnB,aAAO,CAAC,KAAK,CAAC;iCAEd,aAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;wBAEnC,OAEK,IAAI,MAAM,OAAO,EACpB,aAAO,CAAC,KAAK,CAAC;6BAId,aAAO,CAAC,KAAK,CAAC;oBAElB;gBACF;gBAEA,QAAQ;gBACR,qBAAqB;oBACnB,CAAC;4BAWM;wBAVL,QAAQ;wBACR,MAAM,QAAQ,qBAAW,CAAC,QAAQ;wBAClC,IAAI,OACF,OAAO,OAAO,GAAG;4BACf,GAAG,OAAO,OAAO;4BACjB,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;wBAClC;wBAGF,kBAAkB;wBAClB,IAAI,GAAC,kBAAA,OAAO,OAAO,cAAd,sCAAA,eAAgB,CAAC,eAAe,GACnC,OAAO,OAAO,GAAG;4BACf,GAAG,OAAO,OAAO;4BACjB,gBAAgB;wBAClB;wBAGF,OAAO;oBACT;iBACD;gBAED,QAAQ;gBACR,sBAAsB;oBACpB,CAAC;wBACC,iBAAiB;wBACjB,MAAM,EAAE,IAAI,EAAE,GAAG;wBAEjB,kBAAkB;wBAClB,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,WAAW;4BAC5B,eAAe;4BACf,IAAI,KAAK,IAAI,KAAK,KAChB,OAAO;wBAET,0BAA0B;wBAC5B;wBAEA,OAAO;oBACT;iBACD;YACH;;;;;;;;;;;;;;;;;;;;;;;IDxOc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}