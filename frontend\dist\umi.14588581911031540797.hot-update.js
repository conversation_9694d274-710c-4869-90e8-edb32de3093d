globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/request.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TokenManager: function() {
                    return TokenManager;
                },
                apiRequest: function() {
                    return apiRequest;
                },
                // 导出默认请求实例
                default: function() {
                    return _default;
                },
                safeApiRequest: function() {
                    return safeApiRequest;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _umirequest = __mako_require__("node_modules/umi-request/dist/index.esm.js");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            // 创建请求实例
            const request = (0, _umirequest.extend)({
                prefix: '/api',
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            /**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */ class TokenManager {
                static TOKEN_KEY = 'auth_token';
                /**
   * 获取当前Token
   */ static getToken() {
                    return localStorage.getItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 设置Token
   */ static setToken(token) {
                    localStorage.setItem(TokenManager.TOKEN_KEY, token);
                }
                /**
   * 清除Token
   */ static clearToken() {
                    localStorage.removeItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 检查是否有Token
   */ static hasToken() {
                    return !!TokenManager.getToken();
                }
            }
            /**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */ request.interceptors.request.use((url, options)=>{
                const token = TokenManager.getToken();
                if (token) {
                    // 添加Authorization头部
                    const headers = {
                        ...options.headers,
                        Authorization: `Bearer ${token}`
                    };
                    return {
                        url,
                        options: {
                            ...options,
                            headers
                        }
                    };
                }
                return {
                    url,
                    options
                };
            });
            /**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 */ request.interceptors.response.use(async (response)=>{
                const data = await response.clone().json();
                // 检查业务状态码
                if (data.code !== 200) {
                    // 认证失败的处理
                    if (data.code === 401) {
                        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
                        // 可能是Token更新的时序问题，不立即跳转
                        const currentPath = window.location.pathname;
                        const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                        // 如果是Dashboard相关页面，延迟处理认证错误
                        if (isDashboardRelated) {
                            console.warn('Dashboard页面认证失败，可能是Token更新时序问题:', data.message);
                            return Promise.reject(new Error(data.message));
                        }
                        // 其他页面立即处理认证错误
                        TokenManager.clearToken();
                        _antd.message.error('登录已过期，请重新登录');
                        // 跳转到登录页，避免重复跳转
                        if (window.location.pathname !== '/user/login') window.location.href = '/user/login';
                        return Promise.reject(new Error(data.message));
                    }
                    // 其他业务错误，显示错误消息
                    _antd.message.error(data.message || '请求失败');
                    return Promise.reject(new Error(data.message));
                }
                return response;
            }, (error)=>{
                // 网络错误或其他错误
                if (error.response) {
                    const { status } = error.response;
                    if (status === 401) {
                        TokenManager.clearToken();
                        _antd.message.error('登录已过期，请重新登录');
                        if (window.location.pathname !== '/user/login') window.location.href = '/user/login';
                    } else if (status === 403) {
                        var _error_response_data, _error_response;
                        // 检查是否是团队访问被拒绝的特殊错误
                        const errorMessage = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message;
                        if ((errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('停用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('禁用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('不是该团队的成员'))) // 团队访问相关的错误，使用后端返回的具体消息
                        _antd.message.error(errorMessage);
                        else // 其他权限错误
                        _antd.message.error('没有权限访问该资源');
                    } else if (status === 404) _antd.message.error('请求的资源不存在');
                    else if (status >= 500) _antd.message.error('服务器错误，请稍后重试');
                    else _antd.message.error('请求失败');
                } else _antd.message.error('网络错误，请检查网络连接');
                return Promise.reject(error);
            });
            const apiRequest = {
                get: (url, params)=>{
                    return request.get(url, {
                        params
                    });
                },
                post: (url, data)=>{
                    return request.post(url, {
                        data
                    });
                },
                put: (url, data)=>{
                    return request.put(url, {
                        data
                    });
                },
                delete: (url, params)=>{
                    return request.delete(url, {
                        params
                    });
                }
            };
            const safeApiRequest = {
                /**
   * GET请求，自动处理错误响应
   */ get: async (url, params)=>{
                    try {
                        const response = await request.get(url, {
                            params
                        });
                        if (_responseCodes.ResponseCode.isSuccess(response.code)) return response.data;
                        else {
                            (0, _errorHandler.handleApiError)(response);
                            return null;
                        }
                    } catch (error) {
                        // 网络错误等已经在拦截器中处理
                        return null;
                    }
                },
                /**
   * POST请求，自动处理错误响应
   */ post: async (url, data)=>{
                    try {
                        const response = await request.post(url, {
                            data
                        });
                        if (_responseCodes.ResponseCode.isSuccess(response.code)) return response.data;
                        else {
                            (0, _errorHandler.handleApiError)(response);
                            return null;
                        }
                    } catch (error) {
                        // 网络错误等已经在拦截器中处理
                        return null;
                    }
                },
                /**
   * PUT请求，自动处理错误响应
   */ put: async (url, data)=>{
                    try {
                        const response = await request.put(url, {
                            data
                        });
                        if (_responseCodes.ResponseCode.isSuccess(response.code)) return response.data;
                        else {
                            (0, _errorHandler.handleApiError)(response);
                            return null;
                        }
                    } catch (error) {
                        // 网络错误等已经在拦截器中处理
                        return null;
                    }
                },
                /**
   * DELETE请求，自动处理错误响应
   */ delete: async (url, params)=>{
                    try {
                        const response = await request.delete(url, {
                            params
                        });
                        if (_responseCodes.ResponseCode.isSuccess(response.code)) return response.data;
                        else {
                            (0, _errorHandler.handleApiError)(response);
                            return null;
                        }
                    } catch (error) {
                        // 网络错误等已经在拦截器中处理
                        return null;
                    }
                }
            };
            var _default = request;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/constants/responseCodes.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ALL_CODES: function() {
                    return ALL_CODES;
                },
                BAD_GATEWAY: function() {
                    return BAD_GATEWAY;
                },
                BAD_REQUEST: function() {
                    return BAD_REQUEST;
                },
                CLIENT_ERROR_CODES: function() {
                    return CLIENT_ERROR_CODES;
                },
                CONFLICT: function() {
                    return CONFLICT;
                },
                ERROR_CODES: function() {
                    return ERROR_CODES;
                },
                FORBIDDEN: function() {
                    return FORBIDDEN;
                },
                GATEWAY_TIMEOUT: function() {
                    return GATEWAY_TIMEOUT;
                },
                INTERNAL_SERVER_ERROR: function() {
                    return INTERNAL_SERVER_ERROR;
                },
                NOT_FOUND: function() {
                    return NOT_FOUND;
                },
                ResponseCode: function() {
                    return ResponseCode;
                },
                SERVER_ERROR_CODES: function() {
                    return SERVER_ERROR_CODES;
                },
                SERVICE_UNAVAILABLE: function() {
                    return SERVICE_UNAVAILABLE;
                },
                SUCCESS: function() {
                    return SUCCESS;
                },
                SUCCESS_CODES: function() {
                    return SUCCESS_CODES;
                },
                TOO_MANY_REQUESTS: function() {
                    return TOO_MANY_REQUESTS;
                },
                UNAUTHORIZED: function() {
                    return UNAUTHORIZED;
                },
                UNPROCESSABLE_ENTITY: function() {
                    return UNPROCESSABLE_ENTITY;
                },
                default: function() {
                    return _default;
                },
                getDescription: function() {
                    return getDescription;
                },
                isClientError: function() {
                    return isClientError;
                },
                isError: function() {
                    return isError;
                },
                isServerError: function() {
                    return isServerError;
                },
                isSuccess: function() {
                    return isSuccess;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const SUCCESS = 200;
            const BAD_REQUEST = 400;
            const UNAUTHORIZED = 401;
            const FORBIDDEN = 403;
            const NOT_FOUND = 404;
            const CONFLICT = 409;
            const UNPROCESSABLE_ENTITY = 422;
            const TOO_MANY_REQUESTS = 429;
            const INTERNAL_SERVER_ERROR = 500;
            const BAD_GATEWAY = 502;
            const SERVICE_UNAVAILABLE = 503;
            const GATEWAY_TIMEOUT = 504;
            const isSuccess = (code)=>{
                return code === SUCCESS;
            };
            const isClientError = (code)=>{
                return code >= 400 && code < 500;
            };
            const isServerError = (code)=>{
                return code >= 500 && code < 600;
            };
            const isError = (code)=>{
                return !isSuccess(code);
            };
            const getDescription = (code)=>{
                switch(code){
                    case SUCCESS:
                        return '操作成功';
                    case BAD_REQUEST:
                        return '请求参数错误';
                    case UNAUTHORIZED:
                        return '未认证，需要登录';
                    case FORBIDDEN:
                        return '权限不足';
                    case NOT_FOUND:
                        return '资源不存在';
                    case CONFLICT:
                        return '资源冲突';
                    case UNPROCESSABLE_ENTITY:
                        return '请求语义错误';
                    case TOO_MANY_REQUESTS:
                        return '请求频率过高';
                    case INTERNAL_SERVER_ERROR:
                        return '服务器内部错误';
                    case BAD_GATEWAY:
                        return '网关错误';
                    case SERVICE_UNAVAILABLE:
                        return '服务不可用';
                    case GATEWAY_TIMEOUT:
                        return '网关超时';
                    default:
                        return '未知错误';
                }
            };
            const SUCCESS_CODES = [
                SUCCESS
            ];
            const CLIENT_ERROR_CODES = [
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS
            ];
            const SERVER_ERROR_CODES = [
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT
            ];
            const ERROR_CODES = [
                ...CLIENT_ERROR_CODES,
                ...SERVER_ERROR_CODES
            ];
            const ALL_CODES = [
                ...SUCCESS_CODES,
                ...ERROR_CODES
            ];
            const ResponseCode = {
                // 成功状态码
                SUCCESS,
                // 客户端错误状态码
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS,
                // 服务器错误状态码
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT,
                // 工具方法
                isSuccess,
                isClientError,
                isServerError,
                isError,
                getDescription,
                // 代码集合
                SUCCESS_CODES,
                CLIENT_ERROR_CODES,
                SERVER_ERROR_CODES,
                ERROR_CODES,
                ALL_CODES
            };
            var _default = ResponseCode;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/utils/errorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                createErrorHandler: function() {
                    return createErrorHandler;
                },
                // ============= 默认导出 =============
                default: function() {
                    return _default;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                showError: function() {
                    return showError;
                },
                showInfo: function() {
                    return showInfo;
                },
                showNotification: function() {
                    return showNotification;
                },
                showSuccess: function() {
                    return showSuccess;
                },
                showWarning: function() {
                    return showWarning;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                /** 静默处理，不显示任何消息 */ ErrorDisplayType["SILENT"] = "silent";
                /** 使用message.warning显示 */ ErrorDisplayType["WARNING"] = "warning";
                /** 使用message.error显示 */ ErrorDisplayType["ERROR"] = "error";
                /** 使用notification显示 */ ErrorDisplayType["NOTIFICATION"] = "notification";
                /** 重定向到登录页 */ ErrorDisplayType["REDIRECT"] = "redirect";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            /**
 * 默认错误处理配置
 */ const DEFAULT_ERROR_CONFIG = {
                [_responseCodes.ResponseCode.BAD_REQUEST]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.UNAUTHORIZED]: {
                    displayType: "redirect"
                },
                [_responseCodes.ResponseCode.FORBIDDEN]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.NOT_FOUND]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.CONFLICT]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.UNPROCESSABLE_ENTITY]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.TOO_MANY_REQUESTS]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.INTERNAL_SERVER_ERROR]: {
                    displayType: "error",
                    customMessage: '服务器内部错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.BAD_GATEWAY]: {
                    displayType: "error",
                    customMessage: '网关错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.SERVICE_UNAVAILABLE]: {
                    displayType: "error",
                    customMessage: '服务暂时不可用，请稍后重试'
                },
                [_responseCodes.ResponseCode.GATEWAY_TIMEOUT]: {
                    displayType: "error",
                    customMessage: '请求超时，请稍后重试'
                }
            };
            const handleApiError = (response, config)=>{
                if (!response || _responseCodes.ResponseCode.isSuccess(response.code)) return;
                const errorCode = response.code;
                const errorMessage = response.message || (0, _responseCodes.getDescription)(errorCode);
                // 合并配置
                const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {
                    displayType: "error"
                };
                const finalConfig = {
                    ...defaultConfig,
                    ...config
                };
                // 使用自定义消息或响应消息
                const displayMessage = finalConfig.customMessage || errorMessage;
                // 执行错误回调
                if (finalConfig.onError) finalConfig.onError({
                    code: errorCode,
                    message: errorMessage,
                    response
                });
                // 根据显示类型处理错误
                switch(finalConfig.displayType){
                    case "silent":
                        break;
                    case "warning":
                        _antd.message.warning(displayMessage);
                        break;
                    case "error":
                        _antd.message.error(displayMessage);
                        break;
                    case "notification":
                        _antd.notification.error({
                            message: '操作失败',
                            description: displayMessage,
                            duration: 4.5
                        });
                        break;
                    case "redirect":
                        handleAuthError(errorCode, displayMessage);
                        break;
                    default:
                        _antd.message.error(displayMessage);
                        break;
                }
            };
            /**
 * 处理认证相关错误
 * 
 * @param errorCode 错误代码
 * @param errorMessage 错误消息
 */ const handleAuthError = (errorCode, errorMessage)=>{
                if (errorCode === _responseCodes.ResponseCode.UNAUTHORIZED) {
                    // 检查当前路径，避免在某些页面立即跳转
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        return;
                    }
                    // 清除认证信息并跳转到登录页
                    _services.AuthService.clearTokens();
                    _antd.message.error(errorMessage || '登录已过期，请重新登录');
                    _max.history.push('/user/login');
                } else if (errorCode === _responseCodes.ResponseCode.FORBIDDEN) _antd.message.error(errorMessage || '没有权限访问该资源');
            };
            const showSuccess = (msg)=>{
                _antd.message.success(msg);
            };
            const showWarning = (msg)=>{
                _antd.message.warning(msg);
            };
            const showError = (msg)=>{
                _antd.message.error(msg);
            };
            const showInfo = (msg)=>{
                _antd.message.info(msg);
            };
            const showNotification = (title, description, type = 'info')=>{
                _antd.notification[type]({
                    message: title,
                    description,
                    duration: 4.5
                });
            };
            const createErrorHandler = (defaultConfig)=>{
                return (response, config)=>{
                    const finalConfig = {
                        ...defaultConfig,
                        ...config
                    };
                    handleApiError(response, finalConfig);
                };
            };
            var _default = {
                handleApiError,
                showSuccess,
                showWarning,
                showError,
                showInfo,
                showNotification,
                createErrorHandler,
                ErrorDisplayType
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8568870350279300715';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.14588581911031540797.hot-update.js.map