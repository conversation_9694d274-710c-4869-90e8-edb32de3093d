globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/index.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                AuthService: function() {
                    return _auth.AuthService;
                },
                InvitationService: function() {
                    return _invitation.InvitationService;
                },
                SubscriptionService: function() {
                    return _subscription.SubscriptionService;
                },
                TeamService: function() {
                    return _team.TeamService;
                },
                TokenManager: function() {
                    return _request.TokenManager;
                },
                UserService: function() {
                    return _user.UserService;
                },
                apiRequest: function() {
                    return _request.apiRequest;
                },
                authService: function() {
                    return _auth.default;
                },
                invitationService: function() {
                    return _invitation.default;
                },
                safeApiRequest: function() {
                    return _request.safeApiRequest;
                },
                subscriptionService: function() {
                    return _subscription.default;
                },
                teamService: function() {
                    return _team.default;
                },
                userService: function() {
                    return _user.default;
                }
            });
            var _export_star = __mako_require__("@swc/helpers/_/_export_star");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            _export_star._(__mako_require__("src/types/api.ts"), exports);
            var _request = __mako_require__("src/utils/request.ts");
            var _auth = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("src/services/auth.ts"));
            var _subscription = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("src/services/subscription.ts"));
            var _team = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("src/services/team.ts"));
            var _user = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("src/services/user.ts"));
            var _invitation = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("src/services/invitation.ts"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '16001282197663079395';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.8568870350279300715.hot-update.js.map