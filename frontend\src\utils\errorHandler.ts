/**
 * 集中式错误消息处理工具
 * 
 * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。
 * 支持message和notification组件，确保错误消息显示的一致性。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import { message, notification } from 'antd';
import { history } from '@umijs/max';
import { ResponseCode, isError, getDescription } from '@/constants/responseCodes';
import type { ApiResponse } from '@/types/api';
import { AuthService } from '@/services';

// ============= 错误显示类型 =============

/**
 * 错误显示类型枚举
 */
export enum ErrorDisplayType {
  /** 静默处理，不显示任何消息 */
  SILENT = 'silent',
  /** 使用message.warning显示 */
  WARNING = 'warning',
  /** 使用message.error显示 */
  ERROR = 'error',
  /** 使用notification显示 */
  NOTIFICATION = 'notification',
  /** 重定向到登录页 */
  REDIRECT = 'redirect',
}

// ============= 错误处理配置 =============

/**
 * 错误处理配置接口
 */
export interface ErrorHandlerConfig {
  /** 错误显示类型 */
  displayType?: ErrorDisplayType;
  /** 自定义错误消息 */
  customMessage?: string;
  /** 是否显示详细错误信息 */
  showDetails?: boolean;
  /** 错误回调函数 */
  onError?: (error: any) => void;
}

/**
 * 默认错误处理配置
 */
const DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {
  [ResponseCode.BAD_REQUEST]: {
    displayType: ErrorDisplayType.ERROR,
  },
  [ResponseCode.UNAUTHORIZED]: {
    displayType: ErrorDisplayType.REDIRECT,
  },
  [ResponseCode.FORBIDDEN]: {
    displayType: ErrorDisplayType.ERROR,
  },
  [ResponseCode.NOT_FOUND]: {
    displayType: ErrorDisplayType.ERROR,
  },
  [ResponseCode.CONFLICT]: {
    displayType: ErrorDisplayType.WARNING,
  },
  [ResponseCode.UNPROCESSABLE_ENTITY]: {
    displayType: ErrorDisplayType.ERROR,
  },
  [ResponseCode.TOO_MANY_REQUESTS]: {
    displayType: ErrorDisplayType.WARNING,
  },
  [ResponseCode.INTERNAL_SERVER_ERROR]: {
    displayType: ErrorDisplayType.ERROR,
    customMessage: '服务器内部错误，请稍后重试',
  },
  [ResponseCode.BAD_GATEWAY]: {
    displayType: ErrorDisplayType.ERROR,
    customMessage: '网关错误，请稍后重试',
  },
  [ResponseCode.SERVICE_UNAVAILABLE]: {
    displayType: ErrorDisplayType.ERROR,
    customMessage: '服务暂时不可用，请稍后重试',
  },
  [ResponseCode.GATEWAY_TIMEOUT]: {
    displayType: ErrorDisplayType.ERROR,
    customMessage: '请求超时，请稍后重试',
  },
};

// ============= 核心错误处理方法 =============

/**
 * 处理API响应错误
 * 
 * @param response API响应对象
 * @param config 错误处理配置
 */
export const handleApiError = (
  response: ApiResponse<any>,
  config?: ErrorHandlerConfig
): void => {
  if (!response || ResponseCode.isSuccess(response.code)) {
    return;
  }

  const errorCode = response.code;
  const errorMessage = response.message || getDescription(errorCode);
  
  // 合并配置
  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {
    displayType: ErrorDisplayType.ERROR,
  };
  const finalConfig = { ...defaultConfig, ...config };

  // 使用自定义消息或响应消息
  const displayMessage = finalConfig.customMessage || errorMessage;

  // 执行错误回调
  if (finalConfig.onError) {
    finalConfig.onError({ code: errorCode, message: errorMessage, response });
  }

  // 根据显示类型处理错误
  switch (finalConfig.displayType) {
    case ErrorDisplayType.SILENT:
      // 静默处理
      break;
      
    case ErrorDisplayType.WARNING:
      message.warning(displayMessage);
      break;
      
    case ErrorDisplayType.ERROR:
      message.error(displayMessage);
      break;
      
    case ErrorDisplayType.NOTIFICATION:
      notification.error({
        message: '操作失败',
        description: displayMessage,
        duration: 4.5,
      });
      break;
      
    case ErrorDisplayType.REDIRECT:
      handleAuthError(errorCode, displayMessage);
      break;
      
    default:
      message.error(displayMessage);
      break;
  }
};

/**
 * 处理认证相关错误
 * 
 * @param errorCode 错误代码
 * @param errorMessage 错误消息
 */
const handleAuthError = (errorCode: number, errorMessage: string): void => {
  if (errorCode === ResponseCode.UNAUTHORIZED) {
    // 检查当前路径，避免在某些页面立即跳转
    const currentPath = window.location.pathname;
    const isDashboardRelated = 
      currentPath.startsWith('/dashboard') || 
      currentPath.startsWith('/team');

    if (isDashboardRelated) {
      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
      return;
    }

    // 清除认证信息并跳转到登录页
    AuthService.clearTokens();
    message.error(errorMessage || '登录已过期，请重新登录');
    history.push('/user/login');
  } else if (errorCode === ResponseCode.FORBIDDEN) {
    message.error(errorMessage || '没有权限访问该资源');
  }
};

// ============= 便捷方法 =============

/**
 * 显示成功消息
 * 
 * @param message 成功消息
 */
export const showSuccess = (msg: string): void => {
  message.success(msg);
};

/**
 * 显示警告消息
 * 
 * @param message 警告消息
 */
export const showWarning = (msg: string): void => {
  message.warning(msg);
};

/**
 * 显示错误消息
 * 
 * @param message 错误消息
 */
export const showError = (msg: string): void => {
  message.error(msg);
};

/**
 * 显示信息消息
 * 
 * @param message 信息消息
 */
export const showInfo = (msg: string): void => {
  message.info(msg);
};

/**
 * 显示通知
 * 
 * @param title 通知标题
 * @param description 通知描述
 * @param type 通知类型
 */
export const showNotification = (
  title: string,
  description: string,
  type: 'success' | 'info' | 'warning' | 'error' = 'info'
): void => {
  notification[type]({
    message: title,
    description,
    duration: 4.5,
  });
};

// ============= 错误处理Hook =============

/**
 * 创建错误处理器
 * 
 * @param config 默认错误处理配置
 * @returns 错误处理函数
 */
export const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {
  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {
    const finalConfig = { ...defaultConfig, ...config };
    handleApiError(response, finalConfig);
  };
};

// ============= 默认导出 =============

export default {
  handleApiError,
  showSuccess,
  showWarning,
  showError,
  showInfo,
  showNotification,
  createErrorHandler,
  ErrorDisplayType,
};
