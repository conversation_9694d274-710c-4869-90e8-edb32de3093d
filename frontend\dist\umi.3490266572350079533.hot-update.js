globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/requestErrorConfig.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "errorConfig", {
                enumerable: true,
                get: function() {
                    return errorConfig;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorShowType;
            // 错误处理方案： 错误类型（保留兼容性）
            (function(ErrorShowType) {
                ErrorShowType[ErrorShowType["SILENT"] = 0] = "SILENT";
                ErrorShowType[ErrorShowType["WARN_MESSAGE"] = 1] = "WARN_MESSAGE";
                ErrorShowType[ErrorShowType["ERROR_MESSAGE"] = 2] = "ERROR_MESSAGE";
                ErrorShowType[ErrorShowType["NOTIFICATION"] = 3] = "NOTIFICATION";
                ErrorShowType[ErrorShowType["REDIRECT"] = 9] = "REDIRECT";
            })(ErrorShowType || (ErrorShowType = {}));
            const errorConfig = {
                // 错误处理： umi@3 的错误处理方案。
                errorConfig: {
                    // 错误抛出
                    errorThrower: (res)=>{
                        // 检查是否是我们的API响应格式
                        const apiResponse = res;
                        if (apiResponse.code !== undefined) // 使用标准化的响应代码处理
                        {
                            if (!_responseCodes.ResponseCode.isSuccess(apiResponse.code)) {
                                const error = new Error(apiResponse.message);
                                error.name = 'ApiError';
                                error.info = {
                                    errorCode: apiResponse.code,
                                    errorMessage: apiResponse.message,
                                    data: apiResponse.data,
                                    showType: apiResponse.code === _responseCodes.ResponseCode.UNAUTHORIZED ? 9 : 2
                                };
                                throw error;
                            }
                        } else {
                            // 兼容原有格式
                            const { success, data, errorCode, errorMessage, showType } = res;
                            if (!success) {
                                const error = new Error(errorMessage);
                                error.name = 'BizError';
                                error.info = {
                                    errorCode,
                                    errorMessage,
                                    showType,
                                    data
                                };
                                throw error;
                            }
                        }
                    },
                    // 错误接收及处理
                    errorHandler: (error, opts)=>{
                        if (opts === null || opts === void 0 ? void 0 : opts.skipErrorHandler) throw error;
                        // 我们的 API 错误
                        if (error.name === 'ApiError') {
                            const errorInfo = error.info;
                            if (errorInfo) {
                                const { errorMessage, errorCode } = errorInfo;
                                // 处理认证错误
                                if (errorCode === 401) {
                                    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
                                    const currentPath = window.location.pathname;
                                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                                    if (isDashboardRelated) {
                                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                                        // 不立即清除Token和跳转，让页面自己处理
                                        return;
                                    }
                                    // 其他页面立即处理认证错误
                                    _services.AuthService.clearTokens();
                                    _antd.message.error('登录已过期，请重新登录');
                                    _max.history.push('/user/login');
                                    return;
                                }
                                // 处理权限错误
                                if (errorCode === 403) {
                                    // 显示后端返回的具体错误消息
                                    _antd.message.error(errorMessage || '没有权限访问该资源');
                                    return;
                                }
                                // 处理其他业务错误
                                switch(errorInfo.showType){
                                    case 0:
                                        break;
                                    case 1:
                                        _antd.message.warning(errorMessage);
                                        break;
                                    case 2:
                                        _antd.message.error(errorMessage);
                                        break;
                                    case 3:
                                        _antd.notification.open({
                                            description: errorMessage,
                                            message: `错误代码: ${errorCode}`
                                        });
                                        break;
                                    case 9:
                                        _max.history.push('/user/login');
                                        break;
                                    default:
                                        _antd.message.error(errorMessage);
                                }
                            }
                        } else if (error.name === 'BizError') {
                            const errorInfo = error.info;
                            if (errorInfo) {
                                const { errorMessage, errorCode } = errorInfo;
                                switch(errorInfo.showType){
                                    case 0:
                                        break;
                                    case 1:
                                        _antd.message.warning(errorMessage);
                                        break;
                                    case 2:
                                        _antd.message.error(errorMessage);
                                        break;
                                    case 3:
                                        _antd.notification.open({
                                            description: errorMessage,
                                            message: errorCode
                                        });
                                        break;
                                    case 9:
                                        _max.history.push('/user/login');
                                        break;
                                    default:
                                        _antd.message.error(errorMessage);
                                }
                            }
                        } else if (error.response) {
                            const { status } = error.response;
                            if (status === 401) {
                                _services.AuthService.clearTokens();
                                _antd.message.error('登录已过期，请重新登录');
                                _max.history.push('/user/login');
                            } else if (status === 403) _antd.message.error('没有权限访问该资源');
                            else if (status === 404) _antd.message.error('请求的资源不存在');
                            else if (status >= 500) _antd.message.error('服务器错误，请稍后重试');
                            else _antd.message.error(`请求失败: ${status}`);
                        } else if (error.request) _antd.message.error('网络错误，请检查网络连接');
                        else _antd.message.error('请求失败，请重试');
                    }
                },
                // 请求拦截器
                requestInterceptors: [
                    (config)=>{
                        var _config_headers;
                        // 添加认证头
                        const token = _services.AuthService.getToken();
                        if (token) config.headers = {
                            ...config.headers,
                            Authorization: `Bearer ${token}`
                        };
                        // 确保 Content-Type
                        if (!((_config_headers = config.headers) === null || _config_headers === void 0 ? void 0 : _config_headers['Content-Type'])) config.headers = {
                            ...config.headers,
                            'Content-Type': 'application/json'
                        };
                        return config;
                    }
                ],
                // 响应拦截器
                responseInterceptors: [
                    (response)=>{
                        // 拦截响应数据，进行个性化处理
                        const { data } = response;
                        // 检查是否是我们的API响应格式
                        if ((data === null || data === void 0 ? void 0 : data.code) !== undefined) {
                            // 如果是成功响应，直接返回
                            if (data.code === 200) return response;
                        // 错误响应会在 errorThrower 中处理
                        }
                        return response;
                    }
                ]
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/constants/responseCodes.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ALL_CODES: function() {
                    return ALL_CODES;
                },
                BAD_GATEWAY: function() {
                    return BAD_GATEWAY;
                },
                BAD_REQUEST: function() {
                    return BAD_REQUEST;
                },
                CLIENT_ERROR_CODES: function() {
                    return CLIENT_ERROR_CODES;
                },
                CONFLICT: function() {
                    return CONFLICT;
                },
                ERROR_CODES: function() {
                    return ERROR_CODES;
                },
                FORBIDDEN: function() {
                    return FORBIDDEN;
                },
                GATEWAY_TIMEOUT: function() {
                    return GATEWAY_TIMEOUT;
                },
                INTERNAL_SERVER_ERROR: function() {
                    return INTERNAL_SERVER_ERROR;
                },
                NOT_FOUND: function() {
                    return NOT_FOUND;
                },
                ResponseCode: function() {
                    return ResponseCode;
                },
                SERVER_ERROR_CODES: function() {
                    return SERVER_ERROR_CODES;
                },
                SERVICE_UNAVAILABLE: function() {
                    return SERVICE_UNAVAILABLE;
                },
                SUCCESS: function() {
                    return SUCCESS;
                },
                SUCCESS_CODES: function() {
                    return SUCCESS_CODES;
                },
                TOO_MANY_REQUESTS: function() {
                    return TOO_MANY_REQUESTS;
                },
                UNAUTHORIZED: function() {
                    return UNAUTHORIZED;
                },
                UNPROCESSABLE_ENTITY: function() {
                    return UNPROCESSABLE_ENTITY;
                },
                default: function() {
                    return _default;
                },
                getDescription: function() {
                    return getDescription;
                },
                isClientError: function() {
                    return isClientError;
                },
                isError: function() {
                    return isError;
                },
                isServerError: function() {
                    return isServerError;
                },
                isSuccess: function() {
                    return isSuccess;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const SUCCESS = 200;
            const BAD_REQUEST = 400;
            const UNAUTHORIZED = 401;
            const FORBIDDEN = 403;
            const NOT_FOUND = 404;
            const CONFLICT = 409;
            const UNPROCESSABLE_ENTITY = 422;
            const TOO_MANY_REQUESTS = 429;
            const INTERNAL_SERVER_ERROR = 500;
            const BAD_GATEWAY = 502;
            const SERVICE_UNAVAILABLE = 503;
            const GATEWAY_TIMEOUT = 504;
            const isSuccess = (code)=>{
                return code === SUCCESS;
            };
            const isClientError = (code)=>{
                return code >= 400 && code < 500;
            };
            const isServerError = (code)=>{
                return code >= 500 && code < 600;
            };
            const isError = (code)=>{
                return !isSuccess(code);
            };
            const getDescription = (code)=>{
                switch(code){
                    case SUCCESS:
                        return '操作成功';
                    case BAD_REQUEST:
                        return '请求参数错误';
                    case UNAUTHORIZED:
                        return '未认证，需要登录';
                    case FORBIDDEN:
                        return '权限不足';
                    case NOT_FOUND:
                        return '资源不存在';
                    case CONFLICT:
                        return '资源冲突';
                    case UNPROCESSABLE_ENTITY:
                        return '请求语义错误';
                    case TOO_MANY_REQUESTS:
                        return '请求频率过高';
                    case INTERNAL_SERVER_ERROR:
                        return '服务器内部错误';
                    case BAD_GATEWAY:
                        return '网关错误';
                    case SERVICE_UNAVAILABLE:
                        return '服务不可用';
                    case GATEWAY_TIMEOUT:
                        return '网关超时';
                    default:
                        return '未知错误';
                }
            };
            const SUCCESS_CODES = [
                SUCCESS
            ];
            const CLIENT_ERROR_CODES = [
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS
            ];
            const SERVER_ERROR_CODES = [
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT
            ];
            const ERROR_CODES = [
                ...CLIENT_ERROR_CODES,
                ...SERVER_ERROR_CODES
            ];
            const ALL_CODES = [
                ...SUCCESS_CODES,
                ...ERROR_CODES
            ];
            const ResponseCode = {
                // 成功状态码
                SUCCESS,
                // 客户端错误状态码
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS,
                // 服务器错误状态码
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT,
                // 工具方法
                isSuccess,
                isClientError,
                isServerError,
                isError,
                getDescription,
                // 代码集合
                SUCCESS_CODES,
                CLIENT_ERROR_CODES,
                SERVER_ERROR_CODES,
                ERROR_CODES,
                ALL_CODES
            };
            var _default = ResponseCode;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '10734359327999223691';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.3490266572350079533.hot-update.js.map