{"version": 3, "sources": ["umi.14588581911031540797.hot-update.js", "src/utils/request.ts", "src/constants/responseCodes.ts", "src/utils/errorHandler.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='8568870350279300715';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 请求工具类\n * 基于 umi-request 封装，支持双阶段认证\n */\n\nimport { message } from 'antd';\nimport { extend } from 'umi-request';\nimport type { ApiResponse } from '@/types/api';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n// 创建请求实例\nconst request = extend({\n  prefix: '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n/**\n * Token 管理器（单令牌系统）\n *\n * 功能说明：\n * - 统一管理用户认证Token的存储和获取\n * - 使用localStorage进行持久化存储\n * - 支持Token的设置、获取、清除和检查\n *\n * 设计理念：\n * - 采用单令牌系统，简化认证流程\n * - Token同时用于用户认证和团队访问\n * - 提供静态方法，便于全局调用\n */\nclass TokenManager {\n  private static readonly TOKEN_KEY = 'auth_token';\n\n  /**\n   * 获取当前Token\n   */\n  static getToken(): string | null {\n    return localStorage.getItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 设置Token\n   */\n  static setToken(token: string): void {\n    localStorage.setItem(TokenManager.TOKEN_KEY, token);\n  }\n\n  /**\n   * 清除Token\n   */\n  static clearToken(): void {\n    localStorage.removeItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 检查是否有Token\n   */\n  static hasToken(): boolean {\n    return !!TokenManager.getToken();\n  }\n}\n\n/**\n * 请求拦截器\n *\n * 功能：\n * - 自动在请求头中添加Authorization Bearer Token\n * - 统一处理认证信息的注入\n * - 支持无Token的公开接口访问\n */\nrequest.interceptors.request.use((url, options) => {\n  const token = TokenManager.getToken();\n\n  if (token) {\n    // 添加Authorization头部\n    const headers = {\n      ...options.headers,\n      Authorization: `Bearer ${token}`,\n    };\n    return {\n      url,\n      options: { ...options, headers },\n    };\n  }\n\n  return { url, options };\n});\n\n/**\n * 响应拦截器\n *\n * 功能：\n * - 统一处理API响应格式\n * - 自动处理认证失效情况\n * - 统一的错误消息提示\n * - 自动跳转到登录页面\n */\nrequest.interceptors.response.use(\n  async (response) => {\n    const data = await response.clone().json();\n\n    // 检查业务状态码\n    if (data.code !== 200) {\n      // 认证失败的处理\n      if (data.code === 401) {\n        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，\n        // 可能是Token更新的时序问题，不立即跳转\n        const currentPath = window.location.pathname;\n        const isDashboardRelated =\n          currentPath.startsWith('/dashboard') ||\n          currentPath.startsWith('/team');\n\n        // 如果是Dashboard相关页面，延迟处理认证错误\n        if (isDashboardRelated) {\n          console.warn(\n            'Dashboard页面认证失败，可能是Token更新时序问题:',\n            data.message,\n          );\n          return Promise.reject(new Error(data.message));\n        }\n\n        // 其他页面立即处理认证错误\n        TokenManager.clearToken();\n        message.error('登录已过期，请重新登录');\n        // 跳转到登录页，避免重复跳转\n        if (window.location.pathname !== '/user/login') {\n          window.location.href = '/user/login';\n        }\n        return Promise.reject(new Error(data.message));\n      }\n\n      // 其他业务错误，显示错误消息\n      message.error(data.message || '请求失败');\n      return Promise.reject(new Error(data.message));\n    }\n\n    return response;\n  },\n  (error) => {\n    // 网络错误或其他错误\n    if (error.response) {\n      const { status } = error.response;\n      if (status === 401) {\n        TokenManager.clearToken();\n        message.error('登录已过期，请重新登录');\n        if (window.location.pathname !== '/user/login') {\n          window.location.href = '/user/login';\n        }\n      } else if (status === 403) {\n        // 检查是否是团队访问被拒绝的特殊错误\n        const errorMessage = error.response?.data?.message;\n        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {\n          // 团队访问相关的错误，使用后端返回的具体消息\n          message.error(errorMessage);\n        } else {\n          // 其他权限错误\n          message.error('没有权限访问该资源');\n        }\n      } else if (status === 404) {\n        message.error('请求的资源不存在');\n      } else if (status >= 500) {\n        message.error('服务器错误，请稍后重试');\n      } else {\n        message.error('请求失败');\n      }\n    } else {\n      message.error('网络错误，请检查网络连接');\n    }\n\n    return Promise.reject(error);\n  },\n);\n\n// 封装常用的请求方法\nexport const apiRequest = {\n  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.get(url, { params });\n  },\n\n  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.post(url, { data });\n  },\n\n  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.put(url, { data });\n  },\n\n  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.delete(url, { params });\n  },\n};\n\n// 封装带自动错误处理的请求方法\nexport const safeApiRequest = {\n  /**\n   * GET请求，自动处理错误响应\n   */\n  get: async <T = any>(url: string, params?: any): Promise<T | null> => {\n    try {\n      const response = await request.get<ApiResponse<T>>(url, { params });\n      if (ResponseCode.isSuccess(response.code)) {\n        return response.data;\n      } else {\n        handleApiError(response);\n        return null;\n      }\n    } catch (error) {\n      // 网络错误等已经在拦截器中处理\n      return null;\n    }\n  },\n\n  /**\n   * POST请求，自动处理错误响应\n   */\n  post: async <T = any>(url: string, data?: any): Promise<T | null> => {\n    try {\n      const response = await request.post<ApiResponse<T>>(url, { data });\n      if (ResponseCode.isSuccess(response.code)) {\n        return response.data;\n      } else {\n        handleApiError(response);\n        return null;\n      }\n    } catch (error) {\n      // 网络错误等已经在拦截器中处理\n      return null;\n    }\n  },\n\n  /**\n   * PUT请求，自动处理错误响应\n   */\n  put: async <T = any>(url: string, data?: any): Promise<T | null> => {\n    try {\n      const response = await request.put<ApiResponse<T>>(url, { data });\n      if (ResponseCode.isSuccess(response.code)) {\n        return response.data;\n      } else {\n        handleApiError(response);\n        return null;\n      }\n    } catch (error) {\n      // 网络错误等已经在拦截器中处理\n      return null;\n    }\n  },\n\n  /**\n   * DELETE请求，自动处理错误响应\n   */\n  delete: async <T = any>(url: string, params?: any): Promise<T | null> => {\n    try {\n      const response = await request.delete<ApiResponse<T>>(url, { params });\n      if (ResponseCode.isSuccess(response.code)) {\n        return response.data;\n      } else {\n        handleApiError(response);\n        return null;\n      }\n    } catch (error) {\n      // 网络错误等已经在拦截器中处理\n      return null;\n    }\n  },\n};\n\n// 导出 Token 管理器\nexport { TokenManager };\n\n// 导出默认请求实例\nexport default request;\n", "/**\n * 标准化响应代码常量\n * \n * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。\n * 所有API响应处理都应该使用这些预定义的响应代码。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\n// ============= 成功状态码 =============\n\n/**\n * 操作成功\n */\nexport const SUCCESS = 200;\n\n// ============= 客户端错误状态码 =============\n\n/**\n * 请求参数错误或业务逻辑错误\n */\nexport const BAD_REQUEST = 400;\n\n/**\n * 未认证，需要登录\n */\nexport const UNAUTHORIZED = 401;\n\n/**\n * 权限不足，已认证但无权限访问\n */\nexport const FORBIDDEN = 403;\n\n/**\n * 资源不存在\n */\nexport const NOT_FOUND = 404;\n\n/**\n * 资源冲突（如重复创建、数据冲突等）\n */\nexport const CONFLICT = 409;\n\n/**\n * 请求格式正确但语义错误（如验证失败）\n */\nexport const UNPROCESSABLE_ENTITY = 422;\n\n/**\n * 请求频率限制\n */\nexport const TOO_MANY_REQUESTS = 429;\n\n// ============= 服务器错误状态码 =============\n\n/**\n * 服务器内部错误\n */\nexport const INTERNAL_SERVER_ERROR = 500;\n\n/**\n * 网关错误\n */\nexport const BAD_GATEWAY = 502;\n\n/**\n * 服务不可用\n */\nexport const SERVICE_UNAVAILABLE = 503;\n\n/**\n * 网关超时\n */\nexport const GATEWAY_TIMEOUT = 504;\n\n// ============= 响应代码分类方法 =============\n\n/**\n * 判断是否为成功响应代码\n * \n * @param code 响应代码\n * @returns 是否为成功代码\n */\nexport const isSuccess = (code: number): boolean => {\n  return code === SUCCESS;\n};\n\n/**\n * 判断是否为客户端错误代码\n * \n * @param code 响应代码\n * @returns 是否为客户端错误代码\n */\nexport const isClientError = (code: number): boolean => {\n  return code >= 400 && code < 500;\n};\n\n/**\n * 判断是否为服务器错误代码\n * \n * @param code 响应代码\n * @returns 是否为服务器错误代码\n */\nexport const isServerError = (code: number): boolean => {\n  return code >= 500 && code < 600;\n};\n\n/**\n * 判断是否为错误代码（非成功代码）\n * \n * @param code 响应代码\n * @returns 是否为错误代码\n */\nexport const isError = (code: number): boolean => {\n  return !isSuccess(code);\n};\n\n// ============= 响应代码描述方法 =============\n\n/**\n * 获取响应代码的默认描述\n * \n * @param code 响应代码\n * @returns 响应代码描述\n */\nexport const getDescription = (code: number): string => {\n  switch (code) {\n    case SUCCESS:\n      return '操作成功';\n    case BAD_REQUEST:\n      return '请求参数错误';\n    case UNAUTHORIZED:\n      return '未认证，需要登录';\n    case FORBIDDEN:\n      return '权限不足';\n    case NOT_FOUND:\n      return '资源不存在';\n    case CONFLICT:\n      return '资源冲突';\n    case UNPROCESSABLE_ENTITY:\n      return '请求语义错误';\n    case TOO_MANY_REQUESTS:\n      return '请求频率过高';\n    case INTERNAL_SERVER_ERROR:\n      return '服务器内部错误';\n    case BAD_GATEWAY:\n      return '网关错误';\n    case SERVICE_UNAVAILABLE:\n      return '服务不可用';\n    case GATEWAY_TIMEOUT:\n      return '网关超时';\n    default:\n      return '未知错误';\n  }\n};\n\n// ============= 响应代码集合 =============\n\n/**\n * 所有成功响应代码\n */\nexport const SUCCESS_CODES = [SUCCESS];\n\n/**\n * 所有客户端错误响应代码\n */\nexport const CLIENT_ERROR_CODES = [\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n];\n\n/**\n * 所有服务器错误响应代码\n */\nexport const SERVER_ERROR_CODES = [\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n];\n\n/**\n * 所有错误响应代码\n */\nexport const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];\n\n/**\n * 所有响应代码\n */\nexport const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];\n\n// ============= 默认导出 =============\n\n/**\n * 响应代码常量对象\n */\nexport const ResponseCode = {\n  // 成功状态码\n  SUCCESS,\n  \n  // 客户端错误状态码\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n  \n  // 服务器错误状态码\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n  \n  // 工具方法\n  isSuccess,\n  isClientError,\n  isServerError,\n  isError,\n  getDescription,\n  \n  // 代码集合\n  SUCCESS_CODES,\n  CLIENT_ERROR_CODES,\n  SERVER_ERROR_CODES,\n  ERROR_CODES,\n  ALL_CODES,\n} as const;\n\nexport default ResponseCode;\n", "/**\n * 集中式错误消息处理工具\n * \n * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。\n * 支持message和notification组件，确保错误消息显示的一致性。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { ResponseCode, isError, getDescription } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\nimport { AuthService } from '@/services';\n\n// ============= 错误显示类型 =============\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 'silent',\n  /** 使用message.warning显示 */\n  WARNING = 'warning',\n  /** 使用message.error显示 */\n  ERROR = 'error',\n  /** 使用notification显示 */\n  NOTIFICATION = 'notification',\n  /** 重定向到登录页 */\n  REDIRECT = 'redirect',\n}\n\n// ============= 错误处理配置 =============\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否显示详细错误信息 */\n  showDetails?: boolean;\n  /** 错误回调函数 */\n  onError?: (error: any) => void;\n}\n\n/**\n * 默认错误处理配置\n */\nconst DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {\n  [ResponseCode.BAD_REQUEST]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.UNAUTHORIZED]: {\n    displayType: ErrorDisplayType.REDIRECT,\n  },\n  [ResponseCode.FORBIDDEN]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.NOT_FOUND]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.CONFLICT]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.UNPROCESSABLE_ENTITY]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.TOO_MANY_REQUESTS]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.INTERNAL_SERVER_ERROR]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务器内部错误，请稍后重试',\n  },\n  [ResponseCode.BAD_GATEWAY]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '网关错误，请稍后重试',\n  },\n  [ResponseCode.SERVICE_UNAVAILABLE]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务暂时不可用，请稍后重试',\n  },\n  [ResponseCode.GATEWAY_TIMEOUT]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '请求超时，请稍后重试',\n  },\n};\n\n// ============= 核心错误处理方法 =============\n\n/**\n * 处理API响应错误\n * \n * @param response API响应对象\n * @param config 错误处理配置\n */\nexport const handleApiError = (\n  response: ApiResponse<any>,\n  config?: ErrorHandlerConfig\n): void => {\n  if (!response || ResponseCode.isSuccess(response.code)) {\n    return;\n  }\n\n  const errorCode = response.code;\n  const errorMessage = response.message || getDescription(errorCode);\n  \n  // 合并配置\n  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {\n    displayType: ErrorDisplayType.ERROR,\n  };\n  const finalConfig = { ...defaultConfig, ...config };\n\n  // 使用自定义消息或响应消息\n  const displayMessage = finalConfig.customMessage || errorMessage;\n\n  // 执行错误回调\n  if (finalConfig.onError) {\n    finalConfig.onError({ code: errorCode, message: errorMessage, response });\n  }\n\n  // 根据显示类型处理错误\n  switch (finalConfig.displayType) {\n    case ErrorDisplayType.SILENT:\n      // 静默处理\n      break;\n      \n    case ErrorDisplayType.WARNING:\n      message.warning(displayMessage);\n      break;\n      \n    case ErrorDisplayType.ERROR:\n      message.error(displayMessage);\n      break;\n      \n    case ErrorDisplayType.NOTIFICATION:\n      notification.error({\n        message: '操作失败',\n        description: displayMessage,\n        duration: 4.5,\n      });\n      break;\n      \n    case ErrorDisplayType.REDIRECT:\n      handleAuthError(errorCode, displayMessage);\n      break;\n      \n    default:\n      message.error(displayMessage);\n      break;\n  }\n};\n\n/**\n * 处理认证相关错误\n * \n * @param errorCode 错误代码\n * @param errorMessage 错误消息\n */\nconst handleAuthError = (errorCode: number, errorMessage: string): void => {\n  if (errorCode === ResponseCode.UNAUTHORIZED) {\n    // 检查当前路径，避免在某些页面立即跳转\n    const currentPath = window.location.pathname;\n    const isDashboardRelated = \n      currentPath.startsWith('/dashboard') || \n      currentPath.startsWith('/team');\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      return;\n    }\n\n    // 清除认证信息并跳转到登录页\n    AuthService.clearTokens();\n    message.error(errorMessage || '登录已过期，请重新登录');\n    history.push('/user/login');\n  } else if (errorCode === ResponseCode.FORBIDDEN) {\n    message.error(errorMessage || '没有权限访问该资源');\n  }\n};\n\n// ============= 便捷方法 =============\n\n/**\n * 显示成功消息\n * \n * @param message 成功消息\n */\nexport const showSuccess = (msg: string): void => {\n  message.success(msg);\n};\n\n/**\n * 显示警告消息\n * \n * @param message 警告消息\n */\nexport const showWarning = (msg: string): void => {\n  message.warning(msg);\n};\n\n/**\n * 显示错误消息\n * \n * @param message 错误消息\n */\nexport const showError = (msg: string): void => {\n  message.error(msg);\n};\n\n/**\n * 显示信息消息\n * \n * @param message 信息消息\n */\nexport const showInfo = (msg: string): void => {\n  message.info(msg);\n};\n\n/**\n * 显示通知\n * \n * @param title 通知标题\n * @param description 通知描述\n * @param type 通知类型\n */\nexport const showNotification = (\n  title: string,\n  description: string,\n  type: 'success' | 'info' | 'warning' | 'error' = 'info'\n): void => {\n  notification[type]({\n    message: title,\n    description,\n    duration: 4.5,\n  });\n};\n\n// ============= 错误处理Hook =============\n\n/**\n * 创建错误处理器\n * \n * @param config 默认错误处理配置\n * @returns 错误处理函数\n */\nexport const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {\n  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {\n    const finalConfig = { ...defaultConfig, ...config };\n    handleApiError(response, finalConfig);\n  };\n};\n\n// ============= 默认导出 =============\n\nexport default {\n  handleApiError,\n  showSuccess,\n  showWarning,\n  showError,\n  showInfo,\n  showNotification,\n  createErrorHandler,\n  ErrorDisplayType,\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC4QJ,YAAY;2BAAZ;;gBA9FI,UAAU;2BAAV;;gBAgGb,WAAW;gBACX,OAAuB;2BAAvB;;gBA9Ea,cAAc;2BAAd;;;;;yCA/LW;+CACD;kDAEM;iDACE;;;;;;;;;YAE/B,SAAS;YACT,MAAM,UAAU,IAAA,kBAAM,EAAC;gBACrB,QAAQ;gBACR,SAAS;gBACT,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA;;;;;;;;;;;;CAYC,GACD,MAAM;gBACJ,OAAwB,YAAY,aAAa;gBAEjD;;GAEC,GACD,OAAO,WAA0B;oBAC/B,OAAO,aAAa,OAAO,CAAC,aAAa,SAAS;gBACpD;gBAEA;;GAEC,GACD,OAAO,SAAS,KAAa,EAAQ;oBACnC,aAAa,OAAO,CAAC,aAAa,SAAS,EAAE;gBAC/C;gBAEA;;GAEC,GACD,OAAO,aAAmB;oBACxB,aAAa,UAAU,CAAC,aAAa,SAAS;gBAChD;gBAEA;;GAEC,GACD,OAAO,WAAoB;oBACzB,OAAO,CAAC,CAAC,aAAa,QAAQ;gBAChC;YACF;YAEA;;;;;;;CAOC,GACD,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK;gBACrC,MAAM,QAAQ,aAAa,QAAQ;gBAEnC,IAAI,OAAO;oBACT,oBAAoB;oBACpB,MAAM,UAAU;wBACd,GAAG,QAAQ,OAAO;wBAClB,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;oBAClC;oBACA,OAAO;wBACL;wBACA,SAAS;4BAAE,GAAG,OAAO;4BAAE;wBAAQ;oBACjC;gBACF;gBAEA,OAAO;oBAAE;oBAAK;gBAAQ;YACxB;YAEA;;;;;;;;CAQC,GACD,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,KAAK,GAAG,IAAI;gBAExC,UAAU;gBACV,IAAI,KAAK,IAAI,KAAK,KAAK;oBACrB,UAAU;oBACV,IAAI,KAAK,IAAI,KAAK,KAAK;wBACrB,qCAAqC;wBACrC,wBAAwB;wBACxB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;wBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;wBAEzB,4BAA4B;wBAC5B,IAAI,oBAAoB;4BACtB,QAAQ,IAAI,CACV,mCACA,KAAK,OAAO;4BAEd,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM,KAAK,OAAO;wBAC9C;wBAEA,eAAe;wBACf,aAAa,UAAU;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd,gBAAgB;wBAChB,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAC/B,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAEzB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM,KAAK,OAAO;oBAC9C;oBAEA,gBAAgB;oBAChB,aAAO,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;oBAC9B,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM,KAAK,OAAO;gBAC9C;gBAEA,OAAO;YACT,GACA,CAAC;gBACC,YAAY;gBACZ,IAAI,MAAM,QAAQ,EAAE;oBAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;oBACjC,IAAI,WAAW,KAAK;wBAClB,aAAa,UAAU;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAC/B,OAAO,QAAQ,CAAC,IAAI,GAAG;oBAE3B,OAAO,IAAI,WAAW,KAAK;4BAEJ,sBAAA;wBADrB,oBAAoB;wBACpB,MAAM,gBAAe,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO;wBAClD,IAAI,CAAA,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,cACzF,wBAAwB;wBACxB,aAAO,CAAC,KAAK,CAAC;6BAEd,SAAS;wBACT,aAAO,CAAC,KAAK,CAAC;oBAElB,OAAO,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;yBACT,IAAI,UAAU,KACnB,aAAO,CAAC,KAAK,CAAC;yBAEd,aAAO,CAAC,KAAK,CAAC;gBAElB,OACE,aAAO,CAAC,KAAK,CAAC;gBAGhB,OAAO,QAAQ,MAAM,CAAC;YACxB;YAIK,MAAM,aAAa;gBACxB,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAO;gBACnC;gBAEA,MAAM,CAAU,KAAa;oBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK;wBAAE;oBAAK;gBAClC;gBAEA,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAK;gBACjC;gBAEA,QAAQ,CAAU,KAAa;oBAC7B,OAAO,QAAQ,MAAM,CAAC,KAAK;wBAAE;oBAAO;gBACtC;YACF;YAGO,MAAM,iBAAiB;gBAC5B;;GAEC,GACD,KAAK,OAAgB,KAAa;oBAChC,IAAI;wBACF,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAiB,KAAK;4BAAE;wBAAO;wBACjE,IAAI,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACtC,OAAO,SAAS,IAAI;6BACf;4BACL,IAAA,4BAAc,EAAC;4BACf,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,iBAAiB;wBACjB,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,MAAM,OAAgB,KAAa;oBACjC,IAAI;wBACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAiB,KAAK;4BAAE;wBAAK;wBAChE,IAAI,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACtC,OAAO,SAAS,IAAI;6BACf;4BACL,IAAA,4BAAc,EAAC;4BACf,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,iBAAiB;wBACjB,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,KAAK,OAAgB,KAAa;oBAChC,IAAI;wBACF,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAiB,KAAK;4BAAE;wBAAK;wBAC/D,IAAI,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACtC,OAAO,SAAS,IAAI;6BACf;4BACL,IAAA,4BAAc,EAAC;4BACf,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,iBAAiB;wBACjB,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,QAAQ,OAAgB,KAAa;oBACnC,IAAI;wBACF,MAAM,WAAW,MAAM,QAAQ,MAAM,CAAiB,KAAK;4BAAE;wBAAO;wBACpE,IAAI,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACtC,OAAO,SAAS,IAAI;6BACf;4BACL,IAAA,4BAAc,EAAC;4BACf,OAAO;wBACT;oBACF,EAAE,OAAO,OAAO;wBACd,iBAAiB;wBACjB,OAAO;oBACT;gBACF;YACF;gBAMA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC/EF,SAAS;2BAAT;;gBAnIA,WAAW;2BAAX;;gBA1CA,WAAW;2BAAX;;gBAiJA,kBAAkB;2BAAlB;;gBA7HA,QAAQ;2BAAR;;gBAoJA,WAAW;2BAAX;;gBA9JA,SAAS;2BAAT;;gBA0CA,eAAe;2BAAf;;gBAfA,qBAAqB;2BAArB;;gBAtBA,SAAS;2BAAT;;gBAqKA,YAAY;2BAAZ;;gBAtBA,kBAAkB;2BAAlB;;gBA/GA,mBAAmB;2BAAnB;;gBAtDA,OAAO;2BAAP;;gBAmJA,aAAa;2BAAb;;gBA9GA,iBAAiB;2BAAjB;;gBAzBA,YAAY;2BAAZ;;gBAoBA,oBAAoB;2BAApB;;gBA6Lb,OAA4B;2BAA5B;;gBA9Ga,cAAc;2BAAd;;gBAhCA,aAAa;2BAAb;;gBAoBA,OAAO;2BAAP;;gBAVA,aAAa;2BAAb;;gBApBA,SAAS;2BAAT;;;;;;;;;;;;;YArEN,MAAM,UAAU;YAOhB,MAAM,cAAc;YAKpB,MAAM,eAAe;YAKrB,MAAM,YAAY;YAKlB,MAAM,YAAY;YAKlB,MAAM,WAAW;YAKjB,MAAM,uBAAuB;YAK7B,MAAM,oBAAoB;YAO1B,MAAM,wBAAwB;YAK9B,MAAM,cAAc;YAKpB,MAAM,sBAAsB;YAK5B,MAAM,kBAAkB;YAUxB,MAAM,YAAY,CAAC;gBACxB,OAAO,SAAS;YAClB;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,UAAU,CAAC;gBACtB,OAAO,CAAC,UAAU;YACpB;YAUO,MAAM,iBAAiB,CAAC;gBAC7B,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE,OAAO;gBACX;YACF;YAOO,MAAM,gBAAgB;gBAAC;aAAQ;YAK/B,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,cAAc;mBAAI;mBAAuB;aAAmB;YAKlE,MAAM,YAAY;mBAAI;mBAAkB;aAAY;YAOpD,MAAM,eAAe;gBAC1B,QAAQ;gBACR;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCeF,kBAAkB;2BAAlB;;gBAOb,mCAAmC;gBAEnC,OASE;2BATF;;gBA/Ja,cAAc;2BAAd;;gBA8GA,SAAS;2BAAT;;gBASA,QAAQ;2BAAR;;gBAWA,gBAAgB;2BAAhB;;gBAtCA,WAAW;2BAAX;;gBASA,WAAW;2BAAX;;;;;yCAhMyB;wCACd;kDAC8B;6CAE1B;;;;;;;;;;sBAOhB;gBACV,iBAAiB;gBAEjB,wBAAwB;gBAExB,sBAAsB;gBAEtB,qBAAqB;gBAErB,YAAY;eATF,qBAAA;YA6BZ;;CAEC,GACD,MAAM,uBAA2D;gBAC/D,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,YAAY,CAAC,EAAE;oBAC3B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,QAAQ,CAAC,EAAE;oBACvB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,oBAAoB,CAAC,EAAE;oBACnC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,iBAAiB,CAAC,EAAE;oBAChC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,qBAAqB,CAAC,EAAE;oBACpC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,mBAAmB,CAAC,EAAE;oBAClC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,eAAe,CAAC,EAAE;oBAC9B,WAAW;oBACX,eAAe;gBACjB;YACF;YAUO,MAAM,iBAAiB,CAC5B,UACA;gBAEA,IAAI,CAAC,YAAY,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACnD;gBAGF,MAAM,YAAY,SAAS,IAAI;gBAC/B,MAAM,eAAe,SAAS,OAAO,IAAI,IAAA,6BAAc,EAAC;gBAExD,OAAO;gBACP,MAAM,gBAAgB,oBAAoB,CAAC,UAAU,IAAI;oBACvD,WAAW;gBACb;gBACA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,eAAe;gBACf,MAAM,iBAAiB,YAAY,aAAa,IAAI;gBAEpD,SAAS;gBACT,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC;oBAAE,MAAM;oBAAW,SAAS;oBAAc;gBAAS;gBAGzE,aAAa;gBACb,OAAQ,YAAY,WAAW;oBAC7B;wBAEE;oBAEF;wBACE,aAAO,CAAC,OAAO,CAAC;wBAChB;oBAEF;wBACE,aAAO,CAAC,KAAK,CAAC;wBACd;oBAEF;wBACE,kBAAY,CAAC,KAAK,CAAC;4BACjB,SAAS;4BACT,aAAa;4BACb,UAAU;wBACZ;wBACA;oBAEF;wBACE,gBAAgB,WAAW;wBAC3B;oBAEF;wBACE,aAAO,CAAC,KAAK,CAAC;wBACd;gBACJ;YACF;YAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,WAAmB;gBAC1C,IAAI,cAAc,2BAAY,CAAC,YAAY,EAAE;oBAC3C,qBAAqB;oBACrB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oBAEzB,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb;oBACF;oBAEA,gBAAgB;oBAChB,qBAAW,CAAC,WAAW;oBACvB,aAAO,CAAC,KAAK,CAAC,gBAAgB;oBAC9B,YAAO,CAAC,IAAI,CAAC;gBACf,OAAO,IAAI,cAAc,2BAAY,CAAC,SAAS,EAC7C,aAAO,CAAC,KAAK,CAAC,gBAAgB;YAElC;YASO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,YAAY,CAAC;gBACxB,aAAO,CAAC,KAAK,CAAC;YAChB;YAOO,MAAM,WAAW,CAAC;gBACvB,aAAO,CAAC,IAAI,CAAC;YACf;YASO,MAAM,mBAAmB,CAC9B,OACA,aACA,OAAiD,MAAM;gBAEvD,kBAAY,CAAC,KAAK,CAAC;oBACjB,SAAS;oBACT;oBACA,UAAU;gBACZ;YACF;YAUO,MAAM,qBAAqB,CAAC;gBACjC,OAAO,CAAC,UAA4B;oBAClC,MAAM,cAAc;wBAAE,GAAG,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAClD,eAAe,UAAU;gBAC3B;YACF;gBAIA,WAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;;;;;;;;;;;;;;;;;;;;;;;IH1Qc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}