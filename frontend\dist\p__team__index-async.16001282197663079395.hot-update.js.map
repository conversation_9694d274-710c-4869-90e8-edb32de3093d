{"version": 3, "sources": ["p__team__index-async.16001282197663079395.hot-update.js", "src/pages/team/components/TeamListContent.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__team__index',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='1816855265890323774';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队列表内容组件\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport {\n  Avatar,\n  Button,\n  Empty,\n  Input,\n  List,\n  message,\n  Space,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService, TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport { getTeamIdFromCurrentToken, hasTeamInCurrentToken, getUserIdFromCurrentToken } from '@/utils/tokenUtils';\nimport { recordTeamSelection } from '@/utils/teamSelectionUtils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\n\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [currentTeamId, setCurrentTeamId] = useState<number | null>(null);\n\n  useEffect(() => {\n    fetchTeams();\n    // 获取当前团队ID\n    const teamId = getTeamIdFromCurrentToken();\n    setCurrentTeamId(teamId);\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(\n      (team) =>\n        team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        (team.description &&\n          team.description.toLowerCase().includes(searchText.toLowerCase())),\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/personal-center');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n\n      // 响应拦截器已经处理了错误消息显示，这里只需要记录日志\n      // 如果是网络错误或其他非业务错误，才显示通用错误消息\n      if (!error.message || error.message === 'Failed to fetch') {\n        message.error('切换团队失败，请检查网络连接');\n      }\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === team.id\n      ) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 记录用户选择了这个团队\n        const currentUserId = getUserIdFromCurrentToken();\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, team.id);\n        }\n\n        // 更新当前团队ID\n        setCurrentTeamId(team.id);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error: any) {\n      console.error('切换团队失败:', error);\n      // 错误已经在全局错误处理器中处理，这里只需要记录日志\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => {\n            const isCurrentTeam = currentTeamId === team.id;\n            return (\n              <List.Item\n                style={{\n                  backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,\n                  border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,\n                  borderRadius: isCurrentTeam ? '8px' : undefined,\n                  padding: isCurrentTeam ? '16px' : undefined,\n                }}\n                actions={[\n                  <Button\n                    key=\"view\"\n                    type=\"text\"\n                    icon={<EyeOutlined />}\n                    onClick={() => handleViewTeam(team)}\n                  >\n                    查看详情\n                  </Button>,\n                  isCurrentTeam ? (\n                    <Tag\n                      key=\"current\"\n                      color=\"green\"\n                      icon={<CheckCircleOutlined />}\n                    >\n                      当前团队\n                    </Tag>\n                  ) : (\n                    <Button\n                      key=\"switch\"\n                      type=\"primary\"\n                      size=\"small\"\n                      onClick={() => handleSwitchTeam(team)}\n                    >\n                      进入团队\n                    </Button>\n                  ),\n                ]}\n              >\n                <List.Item.Meta\n                  avatar={\n                    <Avatar\n                      size={64}\n                      icon={<TeamOutlined />}\n                      style={{\n                        backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',\n                        border: isCurrentTeam ? '2px solid #389e0d' : undefined,\n                      }}\n                    />\n                  }\n                  title={\n                    <Space>\n                      <Title level={4} style={{ margin: 0 }}>\n                        {team.name}\n                      </Title>\n                      {isCurrentTeam && (\n                        <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                          当前团队\n                        </Tag>\n                      )}\n                      {team.isCreator && (\n                        <Tag color=\"gold\" icon={<CrownOutlined />}>\n                          创建者\n                        </Tag>\n                      )}\n                    </Space>\n                  }\n                  description={\n                    <Space direction=\"vertical\" size=\"small\">\n                      {team.description && (\n                        <Text type=\"secondary\">{team.description}</Text>\n                      )}\n                      <Space>\n                        <Space size=\"small\">\n                          <UserOutlined />\n                          <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                        </Space>\n                        {team.assignedAt && (\n                          <Text type=\"secondary\">\n                            加入于 {new Date(team.assignedAt).toLocaleDateString()}\n                          </Text>\n                        )}\n                        <Text type=\"secondary\">\n                          创建于 {new Date(team.createdAt).toLocaleDateString()}\n                        </Text>\n                      </Space>\n                      <Space>\n                        <Text type=\"secondary\">状态：</Text>\n                        <Tag color={team.isActive ? 'green' : 'red'}>\n                          {team.isActive ? '启用' : '停用'}\n                        </Tag>\n                        {team.lastAccessTime && (\n                          <Text type=\"secondary\">\n                            最后访问：{new Date(team.lastAccessTime).toLocaleDateString()}\n                          </Text>\n                        )}\n                      </Space>\n                    </Space>\n                  }\n                />\n              </List.Item>\n            );\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,kBACA;IACE,SAAS;;;;;;wCC2Rb;;;2BAAA;;;;;;0CAnRO;wCACiB;yCAWjB;oFACoC;6CACF;+CAEmD;uDACxD;;;;;;;;;;YAEpC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;YAIxB,MAAM,kBAA4B;;gBAChC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,IAAA,gBAAS,EAAC;oBACR;oBACA,WAAW;oBACX,MAAM,SAAS,IAAA,qCAAyB;oBACxC,iBAAiB;gBACnB,GAAG,EAAE;gBAEL,IAAA,gBAAS,EAAC;oBACR,SAAS;oBACT,MAAM,WAAW,MAAM,MAAM,CAC3B,CAAC,OACC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,KAAK,WAAW,IACf,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;oBAEpE,iBAAiB;gBACnB,GAAG;oBAAC;oBAAO;iBAAW;gBAEtB,MAAM,aAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;wBAC/C,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,MAAM,iBAAiB,OAAO;oBAC5B,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;4BAAE,QAAQ,KAAK,EAAE;wBAAC;wBAE/D,kBAAkB;wBAClB,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAC5B;4BACA,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;4BACrC,WAAW;4BACX,iBAAiB,KAAK,EAAE;wBACxB,sBAAsB;wBACxB,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,WAAW;wBAEzB,6BAA6B;wBAC7B,4BAA4B;wBAC5B,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,mBACtC,aAAO,CAAC,KAAK,CAAC;oBAElB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;4BAAE,QAAQ,KAAK,EAAE;wBAAC;wBAE/D,kBAAkB;wBAClB,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAC5B;4BACA,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;4BAErC,cAAc;4BACd,MAAM,gBAAgB,IAAA,qCAAyB;4BAC/C,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe,KAAK,EAAE;4BAG5C,WAAW;4BACX,iBAAiB,KAAK,EAAE;4BAExB,0BAA0B;4BAC1B,WAAW;gCACT,YAAO,CAAC,IAAI,CAAC;4BACf,GAAG;wBACL,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,WAAW;oBACzB,4BAA4B;oBAC9B;gBACF;gBAEA,qBACE,2BAAC;;sCACC,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;sCAC7B,cAAA,2BAAC;gCACC,aAAY;gCACZ,UAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,OAAO;oCAAE,OAAO;gCAAI;;;;;;;;;;;wBAIvB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,2BAAC,WAAK;4BACJ,OAAO,WAAK,CAAC,sBAAsB;4BACnC,aACE,aAAa,cAAc;sCAG5B,CAAC,4BACA,2BAAC,YAAM;gCAAC,MAAK;gCAAU,SAAS;0CAAkB;;;;;;;;;;iDAMtD,2BAAC,UAAI;4BACH,SAAS;4BACT,YAAW;4BACX,YAAY;4BACZ,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;4BACxC;4BACA,YAAY,CAAC;gCACX,MAAM,gBAAgB,kBAAkB,KAAK,EAAE;gCAC/C,qBACE,2BAAC,UAAI,CAAC,IAAI;oCACR,OAAO;wCACL,iBAAiB,gBAAgB,YAAY;wCAC7C,QAAQ,gBAAgB,sBAAsB;wCAC9C,cAAc,gBAAgB,QAAQ;wCACtC,SAAS,gBAAgB,SAAS;oCACpC;oCACA,SAAS;sDACP,2BAAC,YAAM;4CAEL,MAAK;4CACL,oBAAM,2BAAC,kBAAW;;;;;4CAClB,SAAS,IAAM,eAAe;sDAC/B;2CAJK;;;;;wCAON,8BACE,2BAAC,SAAG;4CAEF,OAAM;4CACN,oBAAM,2BAAC,0BAAmB;;;;;sDAC3B;2CAHK;;;;mEAON,2BAAC,YAAM;4CAEL,MAAK;4CACL,MAAK;4CACL,SAAS,IAAM,iBAAiB;sDACjC;2CAJK;;;;;qCAQT;8CAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;wCACb,sBACE,2BAAC,YAAM;4CACL,MAAM;4CACN,oBAAM,2BAAC,mBAAY;;;;;4CACnB,OAAO;gDACL,iBAAiB,gBAAgB,YAAY;gDAC7C,QAAQ,gBAAgB,sBAAsB;4CAChD;;;;;;wCAGJ,qBACE,2BAAC,WAAK;;8DACJ,2BAAC;oDAAM,OAAO;oDAAG,OAAO;wDAAE,QAAQ;oDAAE;8DACjC,KAAK,IAAI;;;;;;gDAEX,+BACC,2BAAC,SAAG;oDAAC,OAAM;oDAAQ,oBAAM,2BAAC,0BAAmB;;;;;8DAAK;;;;;;gDAInD,KAAK,SAAS,kBACb,2BAAC,SAAG;oDAAC,OAAM;oDAAO,oBAAM,2BAAC,oBAAa;;;;;8DAAK;;;;;;;;;;;;wCAMjD,2BACE,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAK;;gDAC9B,KAAK,WAAW,kBACf,2BAAC;oDAAK,MAAK;8DAAa,KAAK,WAAW;;;;;;8DAE1C,2BAAC,WAAK;;sEACJ,2BAAC,WAAK;4DAAC,MAAK;;8EACV,2BAAC,mBAAY;;;;;8EACb,2BAAC;oEAAK,MAAK;;wEAAa,KAAK,WAAW;wEAAC;;;;;;;;;;;;;wDAE1C,KAAK,UAAU,kBACd,2BAAC;4DAAK,MAAK;;gEAAY;gEAChB,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;sEAGrD,2BAAC;4DAAK,MAAK;;gEAAY;gEAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;8DAGpD,2BAAC,WAAK;;sEACJ,2BAAC;4DAAK,MAAK;sEAAY;;;;;;sEACvB,2BAAC,SAAG;4DAAC,OAAO,KAAK,QAAQ,GAAG,UAAU;sEACnC,KAAK,QAAQ,GAAG,OAAO;;;;;;wDAEzB,KAAK,cAAc,kBAClB,2BAAC;4DAAK,MAAK;;gEAAY;gEACf,IAAI,KAAK,KAAK,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASxE;;;;;;;;;;;;YAKV;eAzPM;iBAAA;gBA2PN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID3RD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}